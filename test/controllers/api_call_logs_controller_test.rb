require "test_helper"

class ApiCallLogsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @api_call_log = api_call_logs(:one)
  end

  test "should get index" do
    get api_call_logs_url
    assert_response :success
  end

  test "should get new" do
    get new_api_call_log_url
    assert_response :success
  end

  test "should create api_call_log" do
    assert_difference("ApiCallLog.count") do
      post api_call_logs_url, params: { api_call_log: { question_id: @api_call_log.question_id, request_content: @api_call_log.request_content, response_content: @api_call_log.response_content, used_tokens: @api_call_log.used_tokens } }
    end

    assert_redirected_to api_call_log_url(ApiCallLog.last)
  end

  test "should show api_call_log" do
    get api_call_log_url(@api_call_log)
    assert_response :success
  end

  test "should get edit" do
    get edit_api_call_log_url(@api_call_log)
    assert_response :success
  end

  test "should update api_call_log" do
    patch api_call_log_url(@api_call_log), params: { api_call_log: { question_id: @api_call_log.question_id, request_content: @api_call_log.request_content, response_content: @api_call_log.response_content, used_tokens: @api_call_log.used_tokens } }
    assert_redirected_to api_call_log_url(@api_call_log)
  end

  test "should destroy api_call_log" do
    assert_difference("ApiCallLog.count", -1) do
      delete api_call_log_url(@api_call_log)
    end

    assert_redirected_to api_call_logs_url
  end
end
