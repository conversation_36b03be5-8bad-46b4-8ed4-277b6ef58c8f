require "application_system_test_case"

class QuestionsTest < ApplicationSystemTestCase
  setup do
    @question = questions(:one)
  end

  test "visiting the index" do
    visit questions_url
    assert_selector "h1", text: "Questions"
  end

  test "should create question" do
    visit questions_url
    click_on "New question"

    fill_in "Answer", with: @question.answer
    fill_in "Category", with: @question.category
    fill_in "Option 1", with: @question.option_1
    fill_in "Option 2", with: @question.option_2
    fill_in "Option 3", with: @question.option_3
    fill_in "Option 4", with: @question.option_4
    fill_in "Question", with: @question.question
    fill_in "Remark", with: @question.remark
    fill_in "Source", with: @question.source
    fill_in "Usage", with: @question.usage
    fill_in "Which competition", with: @question.which_competition
    click_on "Create Question"

    assert_text "Question was successfully created"
    click_on "Back"
  end

  test "should update Question" do
    visit question_url(@question)
    click_on "Edit this question", match: :first

    fill_in "Answer", with: @question.answer
    fill_in "Category", with: @question.category
    fill_in "Option 1", with: @question.option_1
    fill_in "Option 2", with: @question.option_2
    fill_in "Option 3", with: @question.option_3
    fill_in "Option 4", with: @question.option_4
    fill_in "Question", with: @question.question
    fill_in "Remark", with: @question.remark
    fill_in "Source", with: @question.source
    fill_in "Usage", with: @question.usage
    fill_in "Which competition", with: @question.which_competition
    click_on "Update Question"

    assert_text "Question was successfully updated"
    click_on "Back"
  end

  test "should destroy Question" do
    visit question_url(@question)
    click_on "Destroy this question", match: :first

    assert_text "Question was successfully destroyed"
  end
end
