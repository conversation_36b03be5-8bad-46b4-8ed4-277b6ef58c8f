require "application_system_test_case"

class ApiCallLogsTest < ApplicationSystemTestCase
  setup do
    @api_call_log = api_call_logs(:one)
  end

  test "visiting the index" do
    visit api_call_logs_url
    assert_selector "h1", text: "Api call logs"
  end

  test "should create api call log" do
    visit api_call_logs_url
    click_on "New api call log"

    fill_in "Question", with: @api_call_log.question_id
    fill_in "Request content", with: @api_call_log.request_content
    fill_in "Response content", with: @api_call_log.response_content
    fill_in "Used tokens", with: @api_call_log.used_tokens
    click_on "Create Api call log"

    assert_text "Api call log was successfully created"
    click_on "Back"
  end

  test "should update Api call log" do
    visit api_call_log_url(@api_call_log)
    click_on "Edit this api call log", match: :first

    fill_in "Question", with: @api_call_log.question_id
    fill_in "Request content", with: @api_call_log.request_content
    fill_in "Response content", with: @api_call_log.response_content
    fill_in "Used tokens", with: @api_call_log.used_tokens
    click_on "Update Api call log"

    assert_text "Api call log was successfully updated"
    click_on "Back"
  end

  test "should destroy Api call log" do
    visit api_call_log_url(@api_call_log)
    click_on "Destroy this api call log", match: :first

    assert_text "Api call log was successfully destroyed"
  end
end
