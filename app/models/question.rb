class Question < ApplicationRecord
  require 'open-uri'

  has_many :api_call_logs

  has_one_attached :audio_yue
  has_one_attached :audio

  def self.has_run_grouping?
    # Return false if no any round and no any envelope is set
    Question.where.not(round: nil).or(Question.where.not(envelope: nil)).exists?
  end

  def self.groups
    Question.distinct.pluck(:grouping)
  end

  def self.rounds
    Question.distinct.pluck(:round)
  end

  def self.envelopes(round)
    Question.where(round: round).distinct.pluck(:envelope)
  end

  def tts_content
    "#{question}.  A. #{option_1}.  B. #{option_2}. C. #{option_3}. D. #{option_4}."
  end

  def generate_audio(hd: false, dialect: "", voice: "")

    language = "Chinese"
    language = "Chinese,Yue" if dialect == "yue"

    voice = "Calm_Woman" if voice == ""

    hd_params = ""
    hd_params = "--hd true" if hd

    speed_params = ""
    # speed_params = "--speed 0.95" if dialect == "mandarin"

    request_content = "#{tts_content}\n\n#{hd_params} #{speed_params} --language #{language} --voice #{voice}"

    poe_api = PoeApiService.new
    response = poe_api.chat_completion(request_content)

    if response[:error]
      raise "Failed to generate audio: #{response[:message]}"
    end

    url = "https://" + response["choices"][0]["message"]["content"].split("https://").last

    used_tokens = response["usage"]["total_tokens"]

    ApiCallLog.create(
      question_id: self.id,
      request_content: request_content,
      response_content: response["choices"][0]["message"]["content"],
      used_tokens: used_tokens
    )

    if dialect == "yue"
      self.voice_yue = voice
      self.temp_audio_yue = url
      self.audio_yue.attach(io: URI.open(url), filename: "audio-#{self.id}-yue.mp3")
    else
      self.voice = voice
      self.temp_audio = url
      self.audio.attach(io: URI.open(url), filename: "audio-#{self.id}.mp3")
    end
    self.save
  end

  def previous_id_question
    Question.where("id < ?", self.id).order("id DESC").first
  end

  def next_id_question
    Question.where("id > ?", self.id).order("id ASC").first
  end

end
