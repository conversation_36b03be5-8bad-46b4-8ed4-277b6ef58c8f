class ApiCallLogsController < ApplicationController
  before_action :set_api_call_log, only: %i[ show edit update destroy ]

  # GET /api_call_logs or /api_call_logs.json
  def index
    @api_call_logs = ApiCallLog.all.order(id: :desc)
    @balance = PoeApiService.new.usage_balance
  end

  # GET /api_call_logs/1 or /api_call_logs/1.json
  def show
  end

  # GET /api_call_logs/new
  def new
    @api_call_log = ApiCallLog.new
  end

  # GET /api_call_logs/1/edit
  def edit
  end

  # POST /api_call_logs or /api_call_logs.json
  def create
    @api_call_log = ApiCallLog.new(api_call_log_params)

    respond_to do |format|
      if @api_call_log.save
        format.html { redirect_to @api_call_log, notice: "Api call log was successfully created." }
        format.json { render :show, status: :created, location: @api_call_log }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @api_call_log.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /api_call_logs/1 or /api_call_logs/1.json
  def update
    respond_to do |format|
      if @api_call_log.update(api_call_log_params)
        format.html { redirect_to @api_call_log, notice: "Api call log was successfully updated." }
        format.json { render :show, status: :ok, location: @api_call_log }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @api_call_log.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /api_call_logs/1 or /api_call_logs/1.json
  def destroy
    @api_call_log.destroy!

    respond_to do |format|
      format.html { redirect_to api_call_logs_path, status: :see_other, notice: "Api call log was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_api_call_log
      @api_call_log = ApiCallLog.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def api_call_log_params
      params.require(:api_call_log).permit(:question_id, :request_content, :response_content, :used_tokens)
    end
end
