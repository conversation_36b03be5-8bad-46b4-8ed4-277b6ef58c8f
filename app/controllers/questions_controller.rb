class QuestionsController < ApplicationController
  before_action :set_question, only: %i[ show edit update destroy mark_as_flagged marked_flagged ]

  # GET /questions or /questions.json
  def index
    @questions = Question.all
    if params[:group]
      @questions = @questions.where(grouping: params[:group])
    end
  end

  def run_grouping
    @questions = Question.all
    @questions.each do |question|
      # Patterns for round
      # 第八回合-搶答題, 第八回合-A, 準決賽-第一回合-A, 準決賽-第一回合-搶答題
      # 準決賽-第二回合-C, 準決賽-第二回合-搶答題, Drama7, Drama15, 後備題
      if question.grouping.starts_with?("Drama")
        question.round = "Drama"
      elsif question.grouping == "後備題"
        question.round = "後備題"
      elsif question.grouping.starts_with?("第")
        question.round = question.grouping.split("-").first
        question.envelope = question.grouping.split("-").last
      elsif question.grouping.starts_with?("準決賽")
        question.round = "準決賽" + question.grouping.split("-").second
        question.envelope = question.grouping.split("-").last
      end
      question.save
    end
    redirect_to questions_path, notice: "Grouping is done."
  end

  def generate_audio
    dialect = params[:dialect] || ""
    question = Question.find(params[:id])
    if dialect == "both"
      question.generate_audio hd: true, voice: "Friendly_Person", dialect: "yue"
      question.generate_audio hd: true, voice: "Friendly_Person", dialect: "mandarin"
    else
      question.generate_audio hd: true, voice: "Friendly_Person", dialect: dialect
    end
    if params[:auto_next] == "true"
      next_question = question.next_id_question
      if next_question
        redirect_to generate_audio_question_path(next_question, dialect: dialect, auto_next: "true"), notice: "Audio is generated."
      else
        redirect_to question, notice: "Audio is generated."
      end
    else
      redirect_to question, notice: "Audio is generated."
    end
  end

  # GET /questions/1 or /questions/1.json
  def show
  end

  # GET /questions/new
  def new
    @question = Question.new
  end

  # GET /questions/1/edit
  def edit
  end

  def mark_as_flagged
    @question = Question.find(params[:id])
    @question.is_flagged = true
  end

  def marked_flagged
    question = Question.find(params[:id])
    question.is_flagged = params[:question][:is_flagged]
    question.flagged_remark = params[:question][:flagged_remark]
    question.save
    redirect_to question, notice: "Question is flagged."
  end

  def mark_checked_by_thomas
    question = Question.find(params[:id])
    question.has_checked_by_thomas = true
    question.save
    redirect_to question, notice: "Question is marked as checked by Thomas."
  end

  def unmark_checked_by_thomas
    question = Question.find(params[:id])
    question.has_checked_by_thomas = false
    question.save
    redirect_to question, notice: "Question is unmarked as checked by Thomas."
  end

  # GET /questions/import
  def import
  end

  # POST /questions/import_preview
  def import_preview

    uploaded = params[:file]
    if uploaded.blank?
      redirect_to import_questions_path, alert: "Please choose an .xlsx (or .csv) file to upload."
      return
    end

    # Determine extension and validate supported types
    ext = File.extname(uploaded.original_filename.to_s).downcase
    unless %w[.xlsx .csv].include?(ext)
      redirect_to import_questions_path, alert: "Unsupported file type (#{ext}). Please upload .xlsx or .csv."
      return
    end

    # Persist the uploaded file to tmp so we can re-read it on commit
    tmp_path = Rails.root.join('tmp', "products_import_#{SecureRandom.hex}#{ext}")
    File.binwrite(tmp_path, uploaded.read)

    begin
      parsed = parse_spreadsheet_rows(tmp_path.to_s)
    rescue => e
      redirect_to import_questions_path, alert: "Failed to read file: #{e.message}"
      return
    end

    @tmp_path = tmp_path.to_s
    @headers = parsed[:headers]
    @rows = parsed[:rows]
    @mapping = products_import_mapping
    @unknown_headers = @headers - @mapping.keys

  end

  # POST /questions/import_commit
  def import_commit

    tmp_path = params[:tmp_path]
    if tmp_path.blank? || !File.exist?(tmp_path)
      redirect_to import_questions_path, alert: "Upload session expired. Please upload the file again."
      return
    end

    begin
      parsed = parse_spreadsheet_rows(tmp_path.to_s)
    rescue => e
      redirect_to import_questions_path, alert: "Failed to read file: #{e.message}"
      return
    ensure
      # Clean up the temp file
      File.delete(tmp_path) rescue nil
    end

    mapping = products_import_mapping
    headers = parsed[:headers]

    created = 0
    failed = []

    parsed[:rows].each_with_index do |row, idx|
      attrs = {}
      mapping.each do |header, attr|
        next unless headers.include?(header)
        value = row[header]
        attrs[attr] = value
      end
      # attrs[:company_id] = current_user.company.id
      # attrs[:is_draft] = true

      question = Question.new(attrs)
      if question.save
        created += 1
      else
        failed << { row: idx + 2, errors: question.errors.full_messages }
      end
    end

    if failed.empty?
      redirect_to questions_path, notice: "Imported #{created} questions as drafts."
    else
      flash[:alert] = "Imported #{created} questions. #{failed.size} rows failed."
      @failed = failed
      render :import_result
    end
  end

  # POST /questions or /questions.json
  def create
    @question = Question.new(question_params)

    respond_to do |format|
      if @question.save
        format.html { redirect_to @question, notice: "Question was successfully created." }
        format.json { render :show, status: :created, location: @question }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @question.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /questions/1 or /questions/1.json
  def update
    respond_to do |format|
      if @question.update(question_params)
        format.html { redirect_to @question, notice: "Question was successfully updated." }
        format.json { render :show, status: :ok, location: @question }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @question.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /questions/1 or /questions/1.json
  def destroy
    @question.destroy!

    respond_to do |format|
      format.html { redirect_to questions_path, status: :see_other, notice: "Question was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_question
      @question = Question.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def question_params
      params.require(:question).permit(:which_competition, :usage, :category, :question, :answer, :option_1, :option_2, :option_3, :option_4, :remark, :source, :audio_yue, :audio, :grouping, :round, :seen, :envelope)
    end


    def parse_xlsx_rows(file_path)
      xlsx = Roo::Spreadsheet.open(file_path)
      sheet = xlsx.sheet(0)
      headers = sheet.row(1).map { |h| h.to_s.strip }
      rows = []
      (2..sheet.last_row).each do |i|
        values = sheet.row(i).map { |v| v.is_a?(String) ? v.strip : v }
        rows << headers.zip(values).to_h
      end
      { headers: headers, rows: rows }
    end

    def products_import_mapping
      {
        "屆數" => :which_competition,
        "用途" => :usage,
        "題目分配" => :grouping,
        "科目" => :category,
        "內容" => :question,
        "選擇A." => :option_1,
        "選擇B." => :option_2,
        "選擇C." => :option_3,
        "選擇D." => :option_4,
        "答案" => :answer,
        "答案補充" => :remark,
        "出處" => :source
      }
    end

    def parse_spreadsheet_rows(file_path)
      ext = File.extname(file_path).downcase
      sheet = case ext
              when '.xlsx'
                Roo::Excelx.new(file_path)
              when '.csv'
                Roo::CSV.new(file_path, csv_options: { encoding: 'bom|utf-8' })
              else
                raise "Unsupported file type: #{ext}"
              end
      headers = sheet.row(1).map { |h| h.to_s.strip }
      rows = []
      (2..sheet.last_row).each do |i|
        values = sheet.row(i).map { |v| v.is_a?(String) ? v.to_s.strip : v }
        rows << headers.zip(values).to_h
      end
      { headers: headers, rows: rows }
    end
end
