class HomeController < ApplicationController

  def index
  end

  def show_question
    envelope = params[:envelope]
    round = params[:round]
    id = params[:id]

    if id.present?
      @question = Question.find(id)
      @next_question = Question.where(round: @question.round, envelope: @question.envelope).second
    end

    if envelope.present? && round.present?
      @question = Question.where(round: round, envelope: envelope).first
      @next_question = Question.where(round: round, envelope: envelope).second
    end

    # if params[:id].blank?
    #   question_id = 2
    #   next_question_id = 3
    # else
    #   question_id = params[:id].to_i
    #   next_question_id = question_id + 1
    # end

    # if params[:from].blank?
    #   from_question_id = 0
    # else
    #   from_question_id = params[:from].to_i
    # end

    # @question = Question.find question_id
    # @next_question = Question.find next_question_id
    # if from_question_id == 0
    #   @from_question = nil
    # else
    #   @from_question = Question.find from_question_id
    # end
  end

  def rounds
  end

  def envelopes
    @round = params[:round]
    @envelopes = Question.envelopes(@round)
  end

  def groups
    @groups = ["A", "B", "C", "D", "E", "F", "G", "搶答題", "後備題"]
  end
end
