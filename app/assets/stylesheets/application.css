/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */


body {
    zoom: 1.5;
    font-family: sans-serif;
}

.button {
    display: inline-block;
    padding: .5em 1em;
    background: #eee;
    border-bottom: 1px solid #ccc;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
}

.groups {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.mini.groups {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
}

.groups a {
    display: block;
    text-align: center;
    border: 1px solid #ccc;
    background: #efefef;
    padding: 2em 10px;
    border-radius: 5px;
}

table {
    border-collapse: collapse;
}

table, th, td {
    border: 1px solid lightgray;
    padding: 0.5em;
}