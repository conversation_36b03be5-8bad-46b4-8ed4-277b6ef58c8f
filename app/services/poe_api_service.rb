# app/services/poe_api_service.rb

require 'httparty'

class PoeApiService
  include HTTParty
  base_uri "https://api.poe.com"

  def initialize
    # @api_key = Rails.application.credentials.poe[:api_key]
    @api_key = "-kp9s62nbHqpC5o38HrTiyPVAPKeR4F3WoawG-41e8c"
  end

  def usage_balance
    # 1. Define the headers for this specific request
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{@api_key}"
    }
    # https://api.poe.com/usage/current_balance
    response = self.class.get('/usage/current_balance', headers: headers)
    if response.success?
      response.parsed_response # HTTParty automatically parses JSON responses
    else
      { error: true, code: response.code, message: response.message, body: response.parsed_response }
    end
  end

  def chat_completion(user_content)
    # 1. Define the headers for this specific request
    headers = {
      'Content-Type' => 'application/json',
      'Authorization' => "Bearer #{@api_key}"
    }

    # 2. Construct the payload
    payload = {
      model: "Hailuo-Speech-02",
      messages: [
        {
          role: "user",
          content: user_content
        }
      ]
    }

    # 3. Make the POST request
    # We must convert the body to a JSON string ourselves
    response = self.class.post('/v1/chat/completions',
      headers: headers,
      body: payload.to_json
    )

    # 4. Check the response and return
    if response.success?
      response.parsed_response # HTTParty automatically parses JSON responses
    else
      { error: true, code: response.code, message: response.message, body: response.parsed_response }
    end
  end
end