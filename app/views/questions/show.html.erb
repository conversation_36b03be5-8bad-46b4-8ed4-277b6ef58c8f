<p style="color: green"><%= notice %></p>

<p>
    <% if @question.previous_id_question %>
        <%= link_to "<-", @question.previous_id_question %>
    <% end %>

    <%= @question.id %>

    <% if @question.next_id_question %>
        <%= link_to "->", @question.next_id_question %>
    <% end %>
</p>

<% if @question.is_flagged? %>
    <div class="callout">
        <p>🚩 Flagged Remark: <%= @question.flagged_remark %></p>
    </div>
<% end %>

<div>
    <% if @question.has_checked_by_thomas? %>
        ✔️ TM
        <%= link_to "Unmark", unmark_checked_by_thomas_question_path(@question) %>
    <% else %>
        <%= link_to "Mark as Checked by Thomas", mark_checked_by_thomas_question_path(@question) %>
    <% end %>

    <%= link_to "Mark as Flagged", mark_as_flagged_question_path(@question) %>
</div>

<%= render @question %>



<p>
    <%= link_to "Generate Both Audio", generate_audio_question_path(@question, dialect: "both") %>
    <%= link_to "Generate Both Audio (Auto Next)", generate_audio_question_path(@question, dialect: "both", auto_next: "true") %>
</p>

<textarea>
curl "https://api.poe.com/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer -kp9s62nbHqpC5o38HrTiyPVAPKeR4F3WoawG-41e8c" \
    -d '{
        "model": "Hailuo-Speech-02",
        "messages": [{"role": "user", "content": "<%= @question.tts_content %>\n\n--hd true --language Chinese,Yue --voice Calm_Woman"}]
    }'
</textarea>

<%= link_to "Generate Cantonese Audio", generate_audio_question_path(@question, dialect: "yue") %>

<textarea>
curl "https://api.poe.com/v1/chat/completions" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer -kp9s62nbHqpC5o38HrTiyPVAPKeR4F3WoawG-41e8c" \
    -d '{
        "model": "Hailuo-Speech-02",
        "messages": [{"role": "user", "content": "<%= @question.tts_content %>\n\n--hd true --language Chinese --speed 0.95 --voice Calm_Woman"}]
    }'
</textarea>

<%= link_to "Generate Mandarin Audio", generate_audio_question_path(@question, dialect: "mandarin") %>

<div>
  <%= link_to "Edit this question", edit_question_path(@question) %> |
  <%= link_to "Back to questions", questions_path %>

  <%= button_to "Destroy this question", @question, method: :delete %>
</div>
