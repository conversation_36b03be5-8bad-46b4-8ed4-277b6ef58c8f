<div class="alert alert-danger">
  <%= flash[:alert] %>
</div>

<h1>批量匯入題目（XLSX）</h1>

<p>上傳含有題目資料的 .xlsx 檔案，下一步可先預覽後再確認匯入。</p>

<%= form_with url: import_preview_questions_path, multipart: true, method: :post, data: { turbo: false } do |f| %>
<div>
    <%= f.label :file, '選擇 XLSX 檔案' %>
    <%= f.file_field :file, accept: '.xlsx' %>
</div>
<div class="form-action">
    <%= f.submit '預覽匯入資料' %>
</div>
<% end %>

