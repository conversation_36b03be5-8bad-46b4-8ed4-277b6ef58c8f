<%= form_with(model: question) do |form| %>
  <% if question.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(question.errors.count, "error") %> prohibited this question from being saved:</h2>

      <ul>
        <% question.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :audio_yue, style: "display: block" %>
    <%= form.file_field :audio_yue %>

    <% if question.audio_yue.attached? %>
      <audio src="<%= url_for(question.audio_yue) %>" controls></audio>
    <% end %>
  </div>

  <div>
    <%= form.label :audio, style: "display: block" %>
    <%= form.file_field :audio %>

    <% if question.audio.attached? %>
      <audio src="<%= url_for(question.audio) %>" controls></audio>
    <% end %>
  </div>

  <div>
    <%= form.label :group, style: "display: block" %>
    <%= form.text_field :group %>
  </div>

  <div>
    <%= form.label :which_competition, style: "display: block" %>
    <%= form.number_field :which_competition %>
  </div>

  <div>
    <%= form.label :usage, style: "display: block" %>
    <%= form.text_field :usage %>
  </div>

  <div>
    <%= form.label :category, style: "display: block" %>
    <%= form.text_field :category %>
  </div>

  <div>
    <%= form.label :question, style: "display: block" %>
    <%= form.text_field :question %>
  </div>

  <div>
    <%= form.label :answer, style: "display: block" %>
    <%= form.text_field :answer %>
  </div>

  <div>
    <%= form.label :option_1, style: "display: block" %>
    <%= form.text_field :option_1 %>
  </div>

  <div>
    <%= form.label :option_2, style: "display: block" %>
    <%= form.text_field :option_2 %>
  </div>

  <div>
    <%= form.label :option_3, style: "display: block" %>
    <%= form.text_field :option_3 %>
  </div>

  <div>
    <%= form.label :option_4, style: "display: block" %>
    <%= form.text_field :option_4 %>
  </div>

  <div>
    <%= form.label :remark, style: "display: block" %>
    <%= form.text_field :remark %>
  </div>

  <div>
    <%= form.label :source, style: "display: block" %>
    <%= form.text_field :source %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
