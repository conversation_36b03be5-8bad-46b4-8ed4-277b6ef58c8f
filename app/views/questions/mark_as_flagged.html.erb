<%= form_with(model: @question, url: marked_flagged_question_path(@question), method: :post) do |form| %>
  <% if @question.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(@question.errors.count, "error") %> prohibited this question from being saved:</h2>

      <ul>
        <% @question.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :is_flagged, style: "display: block" %>
    <%= form.check_box :is_flagged, checked: @question.is_flagged %>
  </div>

  <div>
    <%= form.label :flagged_remark, style: "display: block" %>
    <%= form.text_area :flagged_remark, value: @question.flagged_remark, style: "width: 100%; height: 100px" %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>

<%= render @question %>
