<div class="alert alert-danger">
  <%= flash[:alert] %>
</div>

<h1>預覽匯入內容</h1>

<% if @unknown_headers.present? %>
  <p class="warning">
    以下欄位將被忽略：
    <%= @unknown_headers.join(', ') %>
  </p>
<% end %>

<%= form_with url: import_commit_questions_path, method: :post, local: true do |f| %>
  <%= hidden_field_tag :tmp_path, @tmp_path %>

  <div class="table-wrapper">
    <table>
      <thead>
        <tr>
          <% @headers.each do |h| %>
            <th><%= h %></th>
          <% end %>
        </tr>
      </thead>
      <tbody>
        <% @rows.first(100).each do |row| %>
          <tr>
            <% @headers.each do |h| %>
              <td><%= row[h] %></td>
            <% end %>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <% if @rows.size > 100 %>
    <p>只顯示前 100 筆作預覽，實際將匯入 <%= @rows.size %> 筆。</p>
  <% else %>
    <p>將匯入 <%= @rows.size %> 筆。</p>
  <% end %>

  <div class="form-action">
    <%= f.submit '確認匯入（全部將標記為草稿）' %>
    <%= link_to '返回重新選擇檔案', import_questions_path %>
  </div>
<% end %>

