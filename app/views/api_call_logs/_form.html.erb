<%= form_with(model: api_call_log) do |form| %>
  <% if api_call_log.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(api_call_log.errors.count, "error") %> prohibited this api_call_log from being saved:</h2>

      <ul>
        <% api_call_log.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :question_id, style: "display: block" %>
    <%= form.number_field :question_id %>
  </div>

  <div>
    <%= form.label :request_content, style: "display: block" %>
    <%= form.text_area :request_content %>
  </div>

  <div>
    <%= form.label :response_content, style: "display: block" %>
    <%= form.text_area :response_content %>
  </div>

  <div>
    <%= form.label :used_tokens, style: "display: block" %>
    <%= form.number_field :used_tokens %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
