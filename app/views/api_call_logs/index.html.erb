<p style="color: green"><%= notice %></p>

<% content_for :title, "Api call logs" %>

<h1>Api call logs</h1>

<p>Balance: <%= number_with_delimiter @balance["current_point_balance"] %></p>


<div id="api_call_logs">
  <table>
    <tr>
      <th>#</th>
      <th>Question</th>
      <th>Request content</th>
      <th>Response content</th>
      <th>Tokens</th>
    </tr>
  <% @api_call_logs.each do |api_call_log| %>
    <tr>
      <td><%= link_to api_call_log.id, api_call_log %></td>
      <td><%= link_to api_call_log.question_id, api_call_log.question %></td>
      <td><%= api_call_log.request_content[0..30] %></td>
      <td><%= api_call_log.response_content[0..30] %></td>
      <td><%= api_call_log.used_tokens %></td>
    </tr>
  <% end %>
  </table>
</div>

<%= link_to "New api call log", new_api_call_log_path %>
