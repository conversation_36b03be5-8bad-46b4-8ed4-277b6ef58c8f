<h1><%= @question.round %> - <%= @question.envelope %></h1>
<h1><%= @question.question %></h1>

<p>A. <%= @question.option_1 %></p>

<p>B. <%= @question.option_2 %></p>

<p>C. <%= @question.option_3 %></p>

<p>D. <%= @question.option_4 %></p>

<p class="answer" style="font-weight:900">Ans: <%= @question.answer %></p>

<p style="font-style: italic"><%= @question.remark %></p>

<p>Next: <%= link_to @next_question.question, home_show_question_path(id: @next_question.id), id: "next-link" %></p>



<% if @question.audio_yue.attached? %>
    <audio src="<%= url_for(@question.audio_yue) %>" controls></audio>
<% end %>


<script>
document.addEventListener('DOMContentLoaded', function() {
  const audio = document.querySelector('audio');

  // Add keyboard event listener for spacebar and arrow keys
  document.addEventListener('keydown', function(event) {
    // Check if spacebar was pressed (keyCode 32 or event.code 'Space')
    if (event.code === 'Space' || event.keyCode === 32) {
      // Prevent default spacebar behavior (page scroll)
      event.preventDefault();

      if (audio) {
        // Toggle play/pause
        if (audio.paused) {
          audio.play().catch(function(error) {
            console.log('Error playing audio:', error);
          });
        } else {
          audio.pause();
        }
      }
    }

    // Check if left arrow key was pressed
    if (event.code === 'ArrowLeft' || event.keyCode === 37) {
      event.preventDefault();

      if (audio) {
        // Replay audio from beginning
        audio.currentTime = 0;
        audio.play().catch(function(error) {
          console.log('Error playing audio:', error);
        });
      }
    }

    // Check if right arrow key was pressed
    if (event.code === 'ArrowRight' || event.keyCode === 39) {
      event.preventDefault();

      // Find the next question link and navigate to it
      const nextLink = document.getElementById('next-link');
      if (nextLink) {
        window.location.href = nextLink.href;
      }
    }
  });

  if (audio) {

    // Optional: Add visual feedback when audio state changes
    audio.addEventListener('play', function() {
      console.log('Audio started playing');
    });

    audio.addEventListener('pause', function() {
      console.log('Audio paused');
    });

    // Optional: Add visual indicator for keyboard controls
    const indicator = document.createElement('div');
    indicator.innerHTML = '⏯️ SPACEBAR: play/pause | ⬅️ LEFT ARROW: replay from start | ➡️ RIGHT ARROW: next question';
    indicator.style.cssText = 'margin: 10px 0; padding: 8px; background: #f0f0f0; border-radius: 4px; font-size: 14px; color: #666;';
    audio.parentNode.insertBefore(indicator, audio.nextSibling);
  }
});
</script>