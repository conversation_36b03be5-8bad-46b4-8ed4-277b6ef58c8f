Rails.application.routes.draw do
  resources :api_call_logs
  get "home/index"
  get "home/show_question"
  get "home/envelopes"
  get "home/rounds"
  get "home/groups"

  resources :questions do
    get  :run_grouping, on: :collection
    get  :import, on: :collection
    post :import_preview, on: :collection
    post :import_commit, on: :collection

    get  :generate_audio, on: :member
    get  :mark_as_flagged, on: :member
    post :marked_flagged, on: :member

    get  :mark_checked_by_thomas, on: :member
    get  :unmark_checked_by_thomas, on: :member
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/*
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  # Defines the root path route ("/")
  root "home#index"
end
